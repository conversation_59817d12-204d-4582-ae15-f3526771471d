package com.pmw790.power.schema;

import com.nomagic.magicdraw.core.Application;
import com.nomagic.magicdraw.core.Project;
import com.nomagic.uml2.ext.jmi.helpers.StereotypesHelper;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.*;
import com.nomagic.uml2.ext.magicdraw.classes.mdkernel.Class;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdinternalstructures.ConnectorEnd;
import com.nomagic.uml2.ext.magicdraw.compositestructures.mdports.Port;
import com.nomagic.uml2.ext.magicdraw.mdprofiles.Stereotype;
import com.pmw790.power.functions.SysMLStereotypes;
import com.pmw790.power.functions.Utilities;

import java.util.*;

/**
 * Manager class for extracting, caching, and providing access to binding schemas.
 * This class extracts binding patterns from the model's IDP Taxonomy and makes
 * them available to other components of the plugin.
 */
public class BindingSchemaManager {

    //--------------------------------------------------------------------------
    // 1. CLASS VARIABLES AND CONSTRUCTOR
    //--------------------------------------------------------------------------
    // Singleton instance
    private static BindingSchemaManager instance;

    // Cache of schemas by block type name
    private final Map<String, BindingSchema> schemaCache;

    // Cache for binding patterns using combined key for better performance
    private final Map<String, BindingPattern> patternCache;

    // Cache for normalized constraint names
    private final Map<String, String> normalizedNameCache;

    // Property lists extracted from binding schemas
    private final List<String> powerProviderProperties;
    private final List<String> powerConsumerProperties;

    // Performance metrics
    private int cacheHits = 0;
    private int cacheMisses = 0;

    /**
     * Private constructor to enforce singleton pattern
     */
    private BindingSchemaManager() {
        schemaCache = new HashMap<>();
        patternCache = new HashMap<>();
        normalizedNameCache = new HashMap<>();
        powerProviderProperties = new ArrayList<>();
        powerConsumerProperties = new ArrayList<>();
    }

    //--------------------------------------------------------------------------
    // 2. INITIALIZATION METHODS
    //--------------------------------------------------------------------------
    /**
     * Gets the singleton instance of BindingSchemaManager
     *
     * @return The BindingSchemaManager instance
     */
    public static synchronized BindingSchemaManager getInstance() {
        if (instance == null) {
            instance = new BindingSchemaManager();
        }
        return instance;
    }

    /**
     * Initializes the BindingSchemaManager with the current project
     * This should be called when the project is opened
     *
     * @param project The current MagicDraw project
     */
    public void initialize(Project project) {
        // Clear any existing cache
        schemaCache.clear();
        powerProviderProperties.clear();
        powerConsumerProperties.clear();

        if (project == null) {
            Utilities.Log("BindingSchemaManager: Cannot initialize with null project");
            return;
        }

        // Get cached power blocks from Utilities
        Element powerProviderBlock = Utilities.getPowerProviderBlock();
        Element powerConsumerBlock = Utilities.getPowerConsumerBlock();

        if (powerProviderBlock != null) {
            BindingSchema providerSchema = extractBindingSchema(powerProviderBlock);
            if (providerSchema != null) {
                schemaCache.put(Utilities.CLASSIFIER_POWER_PROVIDER, providerSchema);
            }
            // Extract property names from the provider block
            extractPropertyNames(powerProviderBlock, powerProviderProperties);
        } else {
            Utilities.Log("BindingSchemaManager: Power Provider block not found in cache");
        }

        if (powerConsumerBlock != null) {
            BindingSchema consumerSchema = extractBindingSchema(powerConsumerBlock);
            if (consumerSchema != null) {
                schemaCache.put(Utilities.CLASSIFIER_POWER_CONSUMER, consumerSchema);
            }
            // Extract property names from the consumer block
            extractPropertyNames(powerConsumerBlock, powerConsumerProperties);
        } else {
            Utilities.Log("BindingSchemaManager: Power Consumer block not found in cache");
        }
    }

    //--------------------------------------------------------------------------
    // 3. SCHEMA EXTRACTION METHODS
    //--------------------------------------------------------------------------
    /**
     * Extracts binding schema from IDP Taxonomy block element
     *
     * @param blockElement The block element to extract from
     * @return The extracted binding schema
     */
    public BindingSchema extractBindingSchema(Element blockElement) {
        if (!(blockElement instanceof Class)) {
            return null;
        }

        Class block = (Class) blockElement;
        String blockName = block.getName();

        BindingSchema schema = new BindingSchema(blockName);

        // Get the project from the element
        Project project = Application.getInstance().getProject();
        if (project == null) {
            Utilities.Log("BindingSchemaManager: Cannot get project, skipping binding extraction");
            return schema;
        }

        // Get required stereotypes for binding connector identification
        Stereotype bindingConnectorStereotype = StereotypesHelper.getStereotype(
                project, "BindingConnector", SysMLStereotypes.getSysMLProfile());

        if (bindingConnectorStereotype == null) {
            Utilities.Log("BindingSchemaManager: Required stereotypes not found for extracting bindings from " + blockName);
            return schema;
        }

        // Find all connectors that have the BindingConnector stereotype
        for (Element owned : block.getOwnedElement()) {
            // Skip if not a connector
            if (owned.getHumanType() == null || !owned.getHumanType().equals("Binding Connector")) {
                continue;
            }
            // Extract connector information using our specialized method
            try {
                processBindingConnector(owned, schema);
            } catch (Exception e) {
                Utilities.Log("BindingSchemaManager: Error processing binding connector: " + e.getMessage());
            }
        }
        return schema;
    }

    /**
     * Processes a binding connector to extract the binding pattern
     * This method uses a specialized implementation for binding connectors
     *
     * @param connector                    The binding connector element
     * @param schema                       The binding schema to add patterns to
     */
    private void processBindingConnector(Element connector, BindingSchema schema) {
        // Call the normalized version of this method
        processBindingConnectorNormalized(connector, schema);
    }

    /**
     * Gets a binding pattern for the given source and target paths
     * Uses efficient flat caching for better performance
     *
     * @param sourcePath The source path
     * @param targetPath The target path
     * @return The binding pattern, or null if an error occurs
     */
    public BindingPattern getBindingPattern(String sourcePath, String targetPath) {
        // Create cache key
        String cacheKey = sourcePath + "|" + targetPath;

        // Check cache
        if (patternCache.containsKey(cacheKey)) {
            cacheHits++;
            return patternCache.get(cacheKey);
        }

        // Pattern not in cache, create it
        try {
            cacheMisses++;
            BindingPattern pattern = BindingPattern.createFromPaths(sourcePath, targetPath);

            // Add to cache
            patternCache.put(cacheKey, pattern);

            return pattern;
        } catch (Exception e) {
            Utilities.Log("BindingSchemaManager: Error creating binding pattern: " + e.getMessage());
            return null;
        }
    }

    /**
     * Processes a binding connector using the normalized approach
     * This method uses the centralized BindingPattern factory method to handle all pattern types
     * with caching for better performance
     *
     * @param connector                    The binding connector element
     * @param schema                       The binding schema to add patterns to
     */
    private void processBindingConnectorNormalized(Element connector, BindingSchema schema) {
        // Get the connected elements using our specialized method
        List<String> connectedElements = getBindingConnectorElements(connector);

        // Validate that we have exactly two connected elements (source and target)
        if (connectedElements.size() != 2) {
            Utilities.Log("BindingSchemaManager: Connector doesn't have expected format, skipping: " + connector.getID());
            return;
        }

        String sourcePath = connectedElements.get(0);
        String targetPath = connectedElements.get(1);

        // Get the pattern from cache or create a new one
        BindingPattern pattern = getBindingPattern(sourcePath, targetPath);
        if (pattern != null) {
            schema.addPatternToIndexes(pattern);
        }
    }

    //--------------------------------------------------------------------------
    // 4. CONNECTOR PATH HANDLING METHODS
    //--------------------------------------------------------------------------
    /**
     * Gets the connected elements from a binding connector
     * This is a specialized version that handles ports and their types appropriately
     *
     * @param connector                    The connector element
     * @return A list of path strings for the connected elements
     */
    private List<String> getBindingConnectorElements(Element connector) {
        List<String> connectedElements = new ArrayList<>();

        for (Element owned : connector.getOwnedElement()) {
            if (owned instanceof ConnectorEnd) {
                ConnectorEnd connectorEnd = (ConnectorEnd) owned;
                Element role = connectorEnd.getRole();

                // Case: Constraint Property with Port
                if (role instanceof Port) {
                    Port port = (Port) role;
                    if (port.getType() != null) {
                        String portTypeName = Objects.requireNonNull(port.getClassifier()).getName();

                        // Create the typed path: ConstraintPropertyType.PortName
                        String typedPath = portTypeName + "." + port.getName();
                        connectedElements.add(typedPath);
                        continue;
                    }
                }

                // Case: Part property
                else if (role instanceof Property) {
                    Property property = (Property) role;
                    String path = property.getName();
                    connectedElements.add(path);
                }
            }
        }

        return connectedElements;
    }

    /**
     * Extracts property names from a block element and adds them to the provided list
     * This method replicates the logic from Utilities.getPropertyNamesFromBlock
     *
     * @param blockElement The block element to extract properties from
     * @param propertyList The list to add property names to
     */
    private void extractPropertyNames(Element blockElement, List<String> propertyList) {
        if (blockElement == null) {
            return;
        }

        for (Element owned : blockElement.getOwnedElement()) {
            if (owned instanceof Property) {
                Property prop = (Property) owned;

                // Skip constraint properties (same logic as in Utilities)
                if (!isConstraintProperty(prop)) {
                    propertyList.add(prop.getName());
                }
            }
        }
    }

    /**
     * Checks if a property is a constraint property
     * This method replicates the logic from Utilities.isConstraintProperty
     *
     * @param property The property to check
     * @return true if it's a constraint property, false otherwise
     */
    private boolean isConstraintProperty(Property property) {
        if (property == null) {
            return false;
        }

        // Check if the property type is a constraint block
        Type propertyType = property.getType();
        if (propertyType instanceof Class) {
            Class typeClass = (Class) propertyType;

            // Get the project to access stereotypes
            Project project = Application.getInstance().getProject();
            if (project == null) {
                return false;
            }

            // Check if it has the ConstraintBlock stereotype
            Stereotype constraintBlockStereotype = StereotypesHelper.getStereotype(
                    project, "ConstraintBlock", SysMLStereotypes.getSysMLProfile());

            if (constraintBlockStereotype != null) {
                return StereotypesHelper.hasStereotype(typeClass, constraintBlockStereotype);
            }
        }

        return false;
    }

    //--------------------------------------------------------------------------
    // 5. UTILITY METHODS
    //--------------------------------------------------------------------------
    /**
     * Gets a binding schema for a specific block type
     *
     * @param blockType The block type name
     * @return The binding schema, or null if not found
     */
    public BindingSchema getBindingSchema(String blockType) {
        BindingSchema schema = schemaCache.get(blockType);
        if (schema != null) {
            cacheHits++;
        } else {
            cacheMisses++;
            Utilities.Log("BindingSchemaManager: No schema found for " + blockType);
        }
        return schema;
    }

    /**
     * Gets a normalized constraint name with caching
     *
     * @param name The constraint name to normalize
     * @return The normalized name
     */
    public String getNormalizedName(String name) {
        if (name == null) return null;

        return normalizedNameCache.computeIfAbsent(name, key -> {
            cacheMisses++;
            return key.toLowerCase().replace(" ", "_");
        });
    }

    /**
     * Checks if a constraint name matches a class name with flexibility
     * for spaces, underscores, and case differences.
     * Uses caching for better performance.
     *
     * @param patternName The name from the binding pattern
     * @param className The name of the constraint block class
     * @return true if the names match with flexibility, false otherwise
     */
    public boolean constraintNameMatches(String patternName, String className) {
        // Get normalized names from cache
        String normalizedPattern = getNormalizedName(patternName);
        String normalizedClassName = getNormalizedName(className);

        if (normalizedPattern == null || normalizedClassName == null) {
            return false;
        }

        // More precise matching logic
        // 1. Exact match
        if (normalizedPattern.equals(normalizedClassName)) {
            cacheHits++;
            return true;
        }

        // 2. One is a suffix of the other with underscore separator
        if (normalizedPattern.endsWith("_" + normalizedClassName) ||
            normalizedClassName.endsWith("_" + normalizedPattern)) {
            cacheHits++;
            return true;
        }

        // 3. One is a prefix of the other with underscore separator
        if (normalizedPattern.startsWith(normalizedClassName + "_") ||
            normalizedClassName.startsWith(normalizedPattern + "_")) {
            cacheHits++;
            return true;
        }

        // 4. Fallback to substring match for backward compatibility
        boolean result = normalizedPattern.contains(normalizedClassName) ||
                         normalizedClassName.contains(normalizedPattern);
        if (result) {
            cacheHits++;
        } else {
            cacheMisses++;
        }
        return result;
    }

    /**
     * Gets the power provider property names extracted from the binding schema
     *
     * @return List of power provider property names
     */
    public List<String> getPowerProviderProperties() {
        return new ArrayList<>(powerProviderProperties);
    }

    /**
     * Gets the power consumer property names extracted from the binding schema
     *
     * @return List of power consumer property names
     */
    public List<String> getPowerConsumerProperties() {
        return new ArrayList<>(powerConsumerProperties);
    }

    /**
     * Clears all caches
     * This should be called when the project is closed
     */
    public void clearCache() {
        schemaCache.clear();
        patternCache.clear();
        normalizedNameCache.clear();
        powerProviderProperties.clear();
        powerConsumerProperties.clear();
        cacheHits = 0;
        cacheMisses = 0;
    }

    /**
     * Gets cache statistics
     *
     * @return A string containing cache statistics
     */
    public String getCacheStats() {
        int totalRequests = cacheHits + cacheMisses;
        double hitRate = totalRequests > 0 ? (double)cacheHits / totalRequests * 100 : 0;

        return String.format("Cache stats: %d hits, %d misses, %.1f%% hit rate, %d schemas, %d pattern entries, %d normalized names",
                cacheHits, cacheMisses, hitRate, schemaCache.size(), patternCache.size(), normalizedNameCache.size());
    }
}